import React, { ReactNode } from 'react';
import { ChatContextType } from '../../common/type';
import { CheckCircleFilled, CloseCircleFilled } from '@ant-design/icons';
import Loading from './Loading';
import classNames from 'classnames';
import { prefixCls } from './ParseTipUtils';

type Props = {
  parseLoading: boolean;
  parseTip: string;
  currentParseInfo?: ChatContextType;
  parseTimeCost?: number;
  isDeveloper?: boolean;
  isSimpleMode?: boolean;
};

const ParseTip: React.FC<Props> = ({
  isSimpleMode = false,
  parseLoading,
  parseTip,
  currentParseInfo,
  parseTimeCost,
  isDeveloper,
}) => {


  // 创建简化的指标和维度显示节点
  const getSimplifiedTipNode = () => {
    const { dimensions, metrics } = currentParseInfo || {};

    return (
      <div className={`${prefixCls}-tip-content`}>
        {/* 显示指标 */}
        {metrics && metrics.length > 0 && (
          <div className={`${prefixCls}-tip-item`} style={{ display: 'block', marginBottom: '8px' }}>
            <span style={{ marginRight: '8px' }}>•</span>
            指标：{metrics.map(metric => metric.name).join('、')}
          </div>
        )}

        {/* 显示维度 */}
        {dimensions && dimensions.length > 0 && (
          <div className={`${prefixCls}-tip-item`} style={{ display: 'block', marginBottom: '8px' }}>
            <span style={{ marginRight: '8px' }}>•</span>
            维度：{dimensions.map(dimension => dimension.name).join('、')}
          </div>
        )}
      </div>
    );
  };

  const getNode = (tipTitle: ReactNode, tipNode?: ReactNode, failed?: boolean) => {
    return (
      <div className={classNames(`${prefixCls}-parse-tip`, failed && `${prefixCls}-parse-tip-failed`)}>
        <div className={`${prefixCls}-title-bar`}>
          {!failed ? (
            <CheckCircleFilled className={`${prefixCls}-step-icon`} />
          ) : (
            <CloseCircleFilled className={`${prefixCls}-step-error-icon`} />
          )}
          <div className={`${prefixCls}-step-title`}>
            {tipTitle}
            {tipNode === undefined && <Loading />}
          </div>
        </div>
        {(tipNode || tipNode === null) && (
          <div
            className={classNames(
              `${prefixCls}-content-container`,
              tipNode === null && `${prefixCls}-empty-content-container`,
              failed && `${prefixCls}-content-container-failed`
            )}
          >
            {tipNode}
          </div>
        )}
      </div>
    );
  };

  if (parseLoading) {
    return getNode('维度指标中');
  }

  if (parseTip) {
    return getNode(
      <>
        维度指标解析失败
        {!!parseTimeCost && isDeveloper && (
          <span className={`${prefixCls}-title-tip`}>(耗时: {parseTimeCost}ms)</span>
        )}
      </>,
      parseTip,
      true
    );
  }

  if (isSimpleMode || !currentParseInfo) {
    return null;
  }

  const { queryMode } = currentParseInfo || {};

  const tipNode = (
    <div className={`${prefixCls}-tip`}>
      {getSimplifiedTipNode()}
    </div>
  );

  return getNode(
    <>
      维度指标
      {!!parseTimeCost && isDeveloper && (
        <span className={`${prefixCls}-title-tip`}>(耗时: {parseTimeCost}ms)</span>
      )}
    </>,
    queryMode === 'PLAIN_TEXT' ? null : tipNode
  );
};

export default ParseTip;
