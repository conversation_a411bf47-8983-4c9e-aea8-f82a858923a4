import React, { ReactNode } from 'react';
import { ChatContextType } from '../../common/type';
import { CheckCircleFilled, CloseCircleFilled } from '@ant-design/icons';
import Loading from './Loading';
import classNames from 'classnames';
import { prefixCls } from './ParseTipUtils';

type Props = {
  parseLoading: boolean;
  parseTip: string;
  currentParseInfo?: ChatContextType;
  parseTimeCost?: number;
  isDeveloper?: boolean;
  isSimpleMode?: boolean;
};

const ParseTip: React.FC<Props> = ({
  isSimpleMode = false,
  parseLoading,
  parseTip,
  currentParseInfo,
  parseTimeCost,
  isDeveloper,
}) => {


  // 创建简化的指标和维度显示节点
  const getSimplifiedTipNode = () => {
    const { dimensions, metrics } = currentParseInfo || {};

    return (
      <div className={`${prefixCls}-tip-content`}>
        {/* 显示指标 */}
        {metrics && metrics.length > 0 && (
          <div className={`${prefixCls}-tip-item`}>
            <div className={`${prefixCls}-tip-item-name`}>
              <span style={{ marginRight: '8px' }}>•</span>
              指标：
            </div>
            <div className={`${prefixCls}-tip-item-value`}>
              {metrics.map(metric => metric.name).join('、')}
            </div>
          </div>
        )}

        {/* 显示维度 */}
        {dimensions && dimensions.length > 0 && (
          <div className={`${prefixCls}-tip-item`}>
            <div className={`${prefixCls}-tip-item-name`}>
              <span style={{ marginRight: '8px' }}>•</span>
              维度：
            </div>
            <div className={`${prefixCls}-tip-item-value`}>
              {dimensions.map(dimension => dimension.name).join('、')}
            </div>
          </div>
        )}
      </div>
    );
  };

  const getNode = (tipTitle: ReactNode, tipNode?: ReactNode, failed?: boolean) => {
    return (
      <div className={classNames(`${prefixCls}-parse-tip`, failed && `${prefixCls}-parse-tip-failed`)}>
        <div className={`${prefixCls}-title-bar`}>
          {!failed ? (
            <CheckCircleFilled className={`${prefixCls}-step-icon`} />
          ) : (
            <CloseCircleFilled className={`${prefixCls}-step-error-icon`} />
          )}
          <div className={`${prefixCls}-step-title`}>
            {tipTitle}
            {tipNode === undefined && <Loading />}
          </div>
        </div>
        {(tipNode || tipNode === null) && (
          <div
            className={classNames(
              `${prefixCls}-content-container`,
              tipNode === null && `${prefixCls}-empty-content-container`,
              failed && `${prefixCls}-content-container-failed`
            )}
          >
            {tipNode}
          </div>
        )}
      </div>
    );
  };

  if (parseLoading) {
    return getNode('维度指标中');
  }

  if (parseTip) {
    return getNode(
      <>
        维度指标解析失败
        {!!parseTimeCost && isDeveloper && (
          <span className={`${prefixCls}-title-tip`}>(耗时: {parseTimeCost}ms)</span>
        )}
      </>,
      parseTip,
      true
    );
  }

  if (isSimpleMode || parseInfoOptions.length === 0) {
    return null;
  }

  const {
    modelId,
    queryMode,
    properties,
    entity,
    nativeQuery,
    textInfo = '',
  } = currentParseInfo || {};

  const entityAlias = entity?.alias?.[0]?.split('.')?.[0];

  const getFilterContent = (filters: any) => {
    const itemValueClass = `${prefixCls}-tip-item-value`;
    const { startDate, endDate } = dateInfo || {};
    const tipItemOptionClass = classNames(`${prefixCls}-tip-item-option`, {
      [`${prefixCls}-mobile-tip-item-option`]: isMobile,
    });
    return (
      <div className={`${prefixCls}-tip-item-filter-content`}>
        {!!dateInfo && (
          <div className={tipItemOptionClass}>
            <span className={`${prefixCls}-tip-item-filter-name`}>数据时间：</span>
            {nativeQuery ? (
              <span className={itemValueClass}>
                {startDate === endDate ? startDate : `${startDate} ~ ${endDate}`}
              </span>
            ) : (
              <RangePicker
                value={[dayjs(startDate), dayjs(endDate)]}
                onChange={onDateInfoChange}
                format="YYYY-MM-DD"
                renderExtraFooter={() => (
                  <Row gutter={[28, 28]}>
                    {Object.keys(ranges).map(key => (
                      <Col key={key}>
                        <Button
                          size="small"
                          onClick={() => handlePresetClick(ranges[key as RangeKeys])}
                        >
                          {key}
                        </Button>
                      </Col>
                    ))}
                  </Row>
                )}
              />
            )}
          </div>
        )}
        {filters?.map((filter: any, index: number) => (
          <FilterItem
            modelId={modelId!}
            filters={filters}
            filter={filter}
            index={index}
            chatContext={currentParseInfo!}
            entityAlias={entityAlias}
            agentId={agentId}
            integrateSystem={integrateSystem}
            onFiltersChange={onFiltersChange}
            onSwitchEntity={onSwitchEntity}
            key={`${filter.name}_${index}`}
          />
        ))}
      </div>
    );
  };

  const getFiltersNode = () => {
    return (
      <>
        {(!!dateInfo || !!dimensionFilters?.length) && (
          <div className={`${prefixCls}-tip-item`}>
            <div className={`${prefixCls}-tip-item-name`}>筛选条件：</div>
            <div className={`${prefixCls}-tip-item-content`}>
              {getFilterContent(dimensionFilters)}
            </div>
          </div>
        )}
        <Button className={`${prefixCls}-reload`} size="small" onClick={onRefresh}>
          <ReloadOutlined />
          重新查询
        </Button>
      </>
    );
  };

  const { type: agentType } = properties || {};

  const tipNode = (
    <div className={`${prefixCls}-tip`}>
      {getTipNode({ parseInfo: currentParseInfo, dimensionFilters, entityInfo })}
      {!(!!agentType && queryMode !== 'LLM_S2SQL') && getFiltersNode()}
    </div>
  );

  return getNode(
    <>
      维度指标
      {!!parseTimeCost && isDeveloper && (
        <span className={`${prefixCls}-title-tip`}>(耗时: {parseTimeCost}ms)</span>
      )}
    </>,
    // isSimpleMode ? <MarkDown markdown={textInfo} /> : queryMode === 'PLAIN_TEXT' ? null : tipNode
    queryMode === 'PLAIN_TEXT' ? null : tipNode
  );
};

export default ParseTip;
